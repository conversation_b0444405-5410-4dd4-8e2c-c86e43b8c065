import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { formatCurrency } from "@/shared/lib/format-currency";
import { formatDateTime } from "@/shared/lib/format-date";
import { getOrderStatusConfig } from "@/shared/utils/order-status-config";
import { useQueryClient } from "@tanstack/react-query";
import { motion } from "framer-motion";
import {
	AlertCircle,
	Calendar,
	CreditCard,
	Download,
	FileText,
	Hash,
	Minus,
	Package,
	Plus,
	Receipt,
	Send,
	ShoppingCart,
	Tag,
	Truck,
	User,
	UserCheck,
	X,
} from "lucide-react";
import { ORDERS_QUERY_KEYS } from "../data/query-keys";
import { useCouponMutation } from "../hooks/coupon-mutation.hook";
import { useOrderFindDetailedById } from "../hooks/find-detailed-by-id.hook";
import { useSendForBillingMutation } from "../hooks/send-for-billing-mutation.hook";

interface OrderDetailsModalProps {
	isOpen: boolean;
	onClose: () => void;
	orderId: number | null;
}

const EmptyField = ({ icon: Icon, message }: { icon: React.ElementType; message: string }) => (
	<div className="flex items-center gap-2 text-gray-400 text-sm">
		<Icon size={14} />
		<span className="italic">{message}</span>
	</div>
);

const parseMoneyValue = (value: string): number => {
	if (!value) return 0;
	const cleanValue = value
		.replace(/R\$\s?/g, "")
		.replace(/\./g, "")
		.replace(",", ".")
		.trim();
	return parseFloat(cleanValue) || 0;
};

export const OrderDetailsModal = ({ isOpen, onClose, orderId }: OrderDetailsModalProps) => {
	const queryClient = useQueryClient();
	const { data: orderData, isLoading, error } = useOrderFindDetailedById(orderId || 0);
	const order = orderData?.success ? orderData.data : null;

	const { sendForBilling, isLoading: isSendingForBilling } = useSendForBillingMutation({
		onSuccess: () => queryClient.invalidateQueries({ queryKey: ORDERS_QUERY_KEYS.FIND_DETAILED_BY_ID(orderId || 0) }),
	});
	const { getCoupon, isLoading: isGettingCoupon } = useCouponMutation({
		onSuccess: (file: File) => {
			const url = URL.createObjectURL(file);
			window.open(url, "_blank");
			setTimeout(() => URL.revokeObjectURL(url), 1000);
		},
	});

	if (!isOpen || !orderId) return null;

	const statusConfig = order ? getOrderStatusConfig(order.status) : null;
	const StatusIcon = statusConfig?.icon;
	const isInProcessing = ["Em Processamento", "Em processamento", "Processando"].includes(order?.status || "");
	const hasInvoice = order?.invoice && order.invoice.length > 0;
	const isFinalized = order?.status === "Finalizado";

	const handleSendForBilling = async () => orderId && (await sendForBilling(orderId));
	const handleViewCoupon = async () => orderId && (await getCoupon(orderId));

	return (
		<OverlayContainer isVisible={isOpen} onClose={onClose}>
			<motion.dialog
				onClick={e => e.stopPropagation()}
				open={isOpen}
				aria-label="Detalhes do Pedido"
				className="flex flex-col w-full max-w-4xl max-h-[90vh] bg-white rounded-[15px] shadow-lg overflow-hidden"
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2, ease: "easeOut" }}
			>
				{/* Header */}
				<div className="flex items-center justify-between p-4 md:p-6 border-b border-gray-200 bg-gray-50">
					<div className="flex items-center gap-3">
						<div className="bg-mainColor/10 p-2 rounded-full">
							<ShoppingCart size={20} className="text-mainColor" />
						</div>
						<div>
							<h2 className="text-lg md:text-xl font-semibold text-gray-800">Pedido #{orderId}</h2>
							{order && statusConfig && (
								<div className="flex items-center gap-2 mt-1">
									<span
										className={`inline-flex items-center gap-1 px-2 py-1 rounded-xl text-xs font-semibold ${statusConfig.color}`}
									>
										{StatusIcon && <StatusIcon size={12} />}
										{statusConfig.label}
									</span>
								</div>
							)}
						</div>
					</div>
					<div className="flex items-center gap-2">
						{hasInvoice && (
							<Button
								onClick={handleViewCoupon}
								disabled={isGettingCoupon}
								className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
								aria-label="Visualizar cupom fiscal"
							>
								<Download size={16} className="mr-2" />
								{isGettingCoupon ? "Carregando..." : "Ver Cupom"}
							</Button>
						)}
						{isInProcessing && (
							<Button
								onClick={handleSendForBilling}
								disabled={isSendingForBilling}
								className="bg-mainColor hover:bg-mainColor/90 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
								aria-label="Enviar pedido para faturamento"
							>
								<Send size={16} className="mr-2" />
								{isSendingForBilling ? "Enviando..." : "Enviar para Faturamento"}
							</Button>
						)}
						<button onClick={onClose} className="p-2 hover:bg-gray-200 rounded-full transition-colors" aria-label="Fechar modal">
							<X size={20} className="text-gray-500" />
						</button>
					</div>
				</div>

				{/* Content */}
				<div className="flex-1 overflow-y-auto p-4 md:p-6">
					{isLoading ? (
						<div className="flex items-center justify-center py-12">
							<motion.div
								className="w-8 h-8 border-2 border-mainColor border-t-transparent rounded-full"
								animate={{ rotate: 360 }}
								transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
							/>
							<span className="text-gray-500 text-sm ml-3">Carregando detalhes...</span>
						</div>
					) : error || !order ? (
						<div className="flex items-center justify-center py-12">
							<div className="text-center">
								<div className="bg-red-100 p-3 rounded-full w-fit mx-auto mb-3">
									<X size={24} className="text-red-500" />
								</div>
								<h3 className="text-lg font-medium text-gray-800 mb-2">Erro ao carregar</h3>
								<p className="text-gray-500 text-sm">Não foi possível carregar os detalhes do pedido.</p>
							</div>
						</div>
					) : (
						<div className="space-y-6">
							{/* Customer & Financial Summary */}
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								{/* Customer Info */}
								<div className="bg-gray-50 rounded-lg p-4">
									<div className="flex items-center gap-2 mb-3">
										<User size={16} className="text-gray-500" />
										<h3 className="font-medium text-gray-800">Cliente</h3>
									</div>
									<div className="space-y-2">
										{order.customer ? (
											<div className="flex items-center gap-2">
												<UserCheck size={14} className="text-green-500" />
												<p className="font-medium text-gray-900">{order.customer}</p>
											</div>
										) : (
											<EmptyField icon={AlertCircle} message="Cliente não informado" />
										)}
										{order.customerCpfCnpj ? (
											<div className="flex items-center gap-2">
												<FileText size={14} className="text-blue-500" />
												<p className="text-sm text-gray-600">{order.customerCpfCnpj}</p>
											</div>
										) : (
											<EmptyField icon={AlertCircle} message="CPF/CNPJ não informado" />
										)}
									</div>
								</div>
								{/* Financial Summary */}
								<div className="bg-gray-50 rounded-lg p-4">
									<div className="flex items-center gap-2 mb-3">
										<Receipt size={16} className="text-gray-500" />
										<h3 className="font-medium text-gray-800">Resumo Financeiro</h3>
									</div>
									<div className="space-y-2 text-sm">
										<div className="flex justify-between items-center">
											<div className="flex items-center gap-1">
												<Plus size={12} className="text-gray-400" />
												<span className="text-gray-600">Subtotal:</span>
											</div>
											<span className="font-medium">{formatCurrency(parseMoneyValue(order.subtotal))}</span>
										</div>
										{parseMoneyValue(order.shippingCost) > 0 ? (
											<div className="flex justify-between items-center">
												<div className="flex items-center gap-1">
													<Truck size={12} className="text-blue-400" />
													<span className="text-gray-600">Frete:</span>
												</div>
												<span className="font-medium">{formatCurrency(parseMoneyValue(order.shippingCost))}</span>
											</div>
										) : (
											<div className="flex justify-between items-center">
												<div className="flex items-center gap-1">
													<Truck size={12} className="text-gray-300" />
													<span className="text-gray-400 italic">Frete grátis</span>
												</div>
												<span className="font-medium text-green-600">R$ 0,00</span>
											</div>
										)}
										{parseMoneyValue(order.discount) > 0 ? (
											<div className="flex justify-between items-center">
												<div className="flex items-center gap-1">
													<Minus size={12} className="text-red-400" />
													<span className="text-gray-600">Desconto do pedido:</span>
												</div>
												<span className="font-medium text-red-600">-{formatCurrency(parseMoneyValue(order.discount))}</span>
											</div>
										) : (
											<div className="flex justify-between items-center">
												<div className="flex items-center gap-1">
													<Minus size={12} className="text-gray-300" />
													<span className="text-gray-400 italic">Sem desconto</span>
												</div>
												<span className="font-medium text-gray-400">R$ 0,00</span>
											</div>
										)}
										<div className="flex justify-between items-center pt-2 border-t border-gray-200">
											<div className="flex items-center gap-1">
												<Receipt size={12} className="text-green-500" />
												<span className="font-medium text-gray-800">Total:</span>
											</div>
											<span className="font-bold text-green-600 text-lg">{formatCurrency(parseMoneyValue(order.total))}</span>
										</div>
									</div>
								</div>
							</div>
							{/* Salesperson */}
							<div className="bg-blue-50 rounded-lg p-4">
								<div className="flex items-center gap-2 mb-2">
									<User size={16} className="text-blue-600" />
									<h3 className="font-medium text-gray-800">Vendedor</h3>
								</div>
								{order.salesperson ? (
									<div className="flex items-center gap-2">
										<UserCheck size={14} className="text-green-500" />
										<p className="text-gray-900">{order.salesperson}</p>
									</div>
								) : (
									<EmptyField icon={AlertCircle} message="Vendedor não informado" />
								)}
							</div>
							{/* Items */}
							<div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
								<div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
									<div className="flex items-center gap-2">
										<Package size={16} className="text-gray-500" />
										<h3 className="font-medium text-gray-800">Itens do Pedido</h3>
										<span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
											{order.items.length} {order.items.length === 1 ? "item" : "itens"}
										</span>
									</div>
								</div>
								<div className="divide-y divide-gray-200">
									{order.items.map(item => (
										<div key={item.id} className="p-4">
											<div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
												<div className="flex-1">
													<div className="flex items-center gap-2 mb-1">
														<Package size={14} className="text-blue-500" />
														<h4 className="font-medium text-gray-900">{item.product}</h4>
													</div>
													<div className="flex items-center gap-4 text-sm text-gray-600">
														<div className="flex items-center gap-1">
															<Hash size={12} className="text-gray-400" />
															<span>Qtd: {item.quantity}</span>
														</div>
														<div className="flex items-center gap-1">
															<Tag size={12} className="text-gray-400" />
															<span>Preço: {formatCurrency(item.price)}</span>
														</div>
														{item.discount > 0 && (
															<div className="flex items-center gap-1">
																<Tag size={12} className="text-red-400" />
																<span className="text-red-600">Desc: {formatCurrency(item.discount)}</span>
															</div>
														)}
													</div>
												</div>
												<div className="text-right">
													<span className="font-medium text-gray-900">{item.total}</span>
												</div>
											</div>
										</div>
									))}
								</div>
							</div>
							{/* Payments */}
							<div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
								<div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
									<div className="flex items-center gap-2">
										<CreditCard size={16} className="text-gray-500" />
										<h3 className="font-medium text-gray-800">Pagamentos</h3>
										{order.payments && order.payments.length > 0 && (
											<span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">{order.payments.length}</span>
										)}
									</div>
								</div>
								{order.payments && order.payments.length > 0 ? (
									<div className="divide-y divide-gray-200">
										{order.payments.map(payment => (
											<div key={payment.id} className="p-4">
												<div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
													<div className="flex-1">
														<h4 className="font-medium text-gray-900 mb-1">{payment.paymentMethod}</h4>
														{payment.parcel > 1 && (
															<div className="text-sm text-gray-600">Parcela: {payment.parcel}x</div>
														)}
													</div>
													<div className="text-right">
														<span className="font-medium text-green-600">{payment.value}</span>
													</div>
												</div>
											</div>
										))}
									</div>
								) : (
									<div className="p-4">
										<EmptyField icon={AlertCircle} message="Nenhum pagamento registrado" />
									</div>
								)}
							</div>
							{/* Invoice */}
							<div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
								<div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
									<div className="flex items-center gap-2">
										<FileText size={16} className="text-gray-500" />
										<h3 className="font-medium text-gray-800">Notas Fiscais</h3>
										{order.invoice && order.invoice.length > 0 && (
											<span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">{order.invoice.length}</span>
										)}
									</div>
								</div>
								{order.invoice && order.invoice.length > 0 ? (
									<div className="divide-y divide-gray-200">
										{order.invoice.map(invoice => (
											<div key={invoice.id} className="p-4 space-y-3">
												<div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
													<div className="flex-1">
														<h4 className="font-medium text-gray-900 mb-1">NF #{invoice.sequence}</h4>
														<div className="flex items-center gap-4 text-sm text-gray-600">
															<span>Tipo: {invoice.type}</span>
															<span>Status: {invoice.status}</span>
															{invoice.issueDate && <span>Emissão: {formatDateTime(invoice.issueDate)}</span>}
														</div>
													</div>
												</div>
												{invoice.key && (
													<div className="bg-gray-50 rounded p-3">
														<div className="text-xs text-gray-600 mb-1">Chave de Acesso:</div>
														<div className="font-mono text-sm text-gray-800 break-all">{invoice.key}</div>
													</div>
												)}
												{invoice.supplier && (
													<div className="text-sm">
														<span className="text-gray-600">Fornecedor: </span>
														<span className="text-gray-900">{invoice.supplier}</span>
													</div>
												)}
												{invoice.events && invoice.events.length > 0 && (
													<div>
														<h5 className="text-sm font-medium text-gray-700 mb-2">Eventos:</h5>
														<div className="space-y-2">
															{invoice.events.map(event => (
																<div key={event.id} className="bg-gray-50 rounded p-2 text-sm">
																	<div className="flex items-center gap-2 mb-1">
																		<Calendar size={12} className="text-gray-500" />
																		<span className="text-gray-600">{formatDateTime(event.date)}</span>
																	</div>
																	<p className="text-gray-800">{event.reason}</p>
																</div>
															))}
														</div>
													</div>
												)}
											</div>
										))}
									</div>
								) : (
									<div className="p-4">
										<EmptyField icon={AlertCircle} message="Nenhuma nota fiscal emitida" />
									</div>
								)}
							</div>
						</div>
					)}
				</div>
			</motion.dialog>
		</OverlayContainer>
	);
};
