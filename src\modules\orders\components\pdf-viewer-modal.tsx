import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { useAtom } from "jotai";
import { Download, Printer, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { pdfViewerState } from "../states/pdf-viewer.state";

function useIsMobile() {
	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		if (typeof window !== "undefined") {
			setIsMobile(/Mobi|Android/i.test(window.navigator.userAgent));
		}
	}, []);

	return isMobile;
}

export function PdfViewerModal() {
	const [pdfFile, setPdfFile] = useAtom(pdfViewerState);
	const [pdfUrl, setPdfUrl] = useState<string | null>(null);
	const isMobile = useIsMobile();
	const open = Boolean(pdfFile);
	const onClose = () => setPdfFile(null);

	const iframeRef = useRef<HTMLIFrameElement | null>(null);

	useEffect(() => {
		if (!pdfFile) return;

		const blob = pdfFile instanceof Blob ? pdfFile : new Blob([pdfFile], { type: "application/pdf" });
		const url = URL.createObjectURL(blob);
		setPdfUrl(url);

		return () => {
			URL.revokeObjectURL(url);
			setPdfUrl(null);
		};
	}, [pdfFile]);

	const handlePrint = () => {
		if (pdfUrl && iframeRef.current) {
			iframeRef.current.contentWindow?.print();
		}
	};

	const handleDownload = () => {
		if (pdfUrl) {
			const link = document.createElement("a");
			link.href = pdfUrl;
			link.download = "cupom-fiscal.pdf";
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		}
	};

	if (!pdfFile) return null;

	return (
		<OverlayContainer isVisible={open} onClose={onClose}>
			<div
				className="bg-white rounded-xl m-4 w-[90vw] h-[90vh] shadow-xl flex flex-col"
				role="dialog"
				aria-modal="true"
				aria-label="Visualizador de PDF - Cupom Fiscal"
				onClick={e => e.stopPropagation()}
			>
				{/* Header */}
				<header className="flex items-center justify-between px-6 py-4 bg-mainColor text-white rounded-t-xl">
					<div className="flex items-center gap-3">
						<h2 className="font-semibold text-xl">Cupom Fiscal</h2>
					</div>
					
					<div className="flex items-center gap-2">
						{/* Botões de ação */}
						{!isMobile && pdfUrl && (
							<>
								<Button
									onClick={handlePrint}
									className="bg-white/20 hover:bg-white/30 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors"
									aria-label="Imprimir cupom"
								>
									<Printer size={16} className="mr-2" />
									Imprimir
								</Button>
								
								<Button
									onClick={handleDownload}
									className="bg-white/20 hover:bg-white/30 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors"
									aria-label="Baixar cupom"
								>
									<Download size={16} className="mr-2" />
									Baixar
								</Button>
							</>
						)}
						
						<button 
							onClick={onClose} 
							className="hover:bg-white/20 p-2 rounded-full transition-colors" 
							aria-label="Fechar modal"
						>
							<X size={20} />
						</button>
					</div>
				</header>

				{/* Content */}
				<main className="flex-1 bg-gray-100 overflow-auto p-4">
					{!pdfUrl ? (
						<div className="flex items-center justify-center h-full">
							<div className="text-center">
								<div className="w-8 h-8 border-2 border-mainColor border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
								<p className="text-gray-600">Carregando PDF...</p>
							</div>
						</div>
					) : isMobile ? (
						<div className="flex flex-col items-center justify-center h-full space-y-4">
							<div className="text-center">
								<div className="text-6xl mb-4">📱</div>
								<h3 className="text-lg font-medium text-gray-800 mb-2">Dispositivo Móvel Detectado</h3>
								<p className="text-gray-600 mb-4">A visualização de PDF pode não funcionar corretamente em dispositivos móveis.</p>
								<p className="text-gray-600 mb-6">Utilize o botão abaixo para baixar o arquivo.</p>
							</div>
							
							<Button
								onClick={handleDownload}
								className="bg-mainColor hover:bg-mainColor/90 text-white px-6 py-3 rounded-lg font-medium"
								aria-label="Baixar cupom fiscal"
							>
								<Download size={20} className="mr-2" />
								Baixar Cupom Fiscal
							</Button>
						</div>
					) : (
						<>
							<embed 
								src={pdfUrl} 
								type="application/pdf" 
								width="100%" 
								height="100%" 
								className="w-full h-full rounded shadow-sm" 
							/>
							{/* iframe oculto para impressão */}
							<iframe 
								ref={iframeRef} 
								src={pdfUrl} 
								style={{ display: "none" }} 
								title="PDF para impressão" 
							/>
						</>
					)}
				</main>
			</div>
		</OverlayContainer>
	);
}
